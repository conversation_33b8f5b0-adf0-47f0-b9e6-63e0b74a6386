# Package 目录

本目录用于存储从GitHub克隆的第三方开源项目和外部服务，按照[外部服务管理规范](../Documents/外部服务管理规范.md)进行管理。

## 📦 已安装项目

### MarkItDown
- **来源**: Microsoft MarkItDown
- **用途**: 文档转换工具，支持多种格式转换为Markdown
- **安装日期**: 2025-07-22
- **依赖**: 已安装在项目venv中
- **状态**: ✅ 正常运行

### kepano-obsidian
- **来源**: Kepano Obsidian相关工具
- **用途**: Obsidian相关的扩展和工具
- **安装日期**: 2025-07-24
- **状态**: ✅ 正常运行

### bazi
- **来源**: https://github.com/china-testing/bazi
- **用途**: 中国传统八字命理学计算工具
- **功能**: 
  - 八字排盘 (`bazi.py`)
  - 生肖合婚 (`shengxiao.py`) 
  - 罗喉日时计算 (`luohou.py`)
- **安装日期**: 2025-08-04
- **依赖**: bidict, lunar_python, colorama, sxtwl (已安装在项目venv中)
- **状态**: ✅ 正常运行

## 🔧 使用方式

所有项目的Python依赖都统一安装在项目根目录的venv虚拟环境中：

```bash
# 激活虚拟环境
source /Users/<USER>/Downloads/Ming-Digital-Garden/venv/bin/activate

# 运行bazi项目示例
cd Package/bazi
python bazi.py 1977 8 11 19 -n    # 八字排盘
python shengxiao.py 龙             # 生肖合婚
python luohou.py                   # 罗喉日时
```

## 📋 管理原则

1. **保持项目原名**: 与GitHub仓库名一致
2. **完整克隆**: 包含LICENSE、README等文件
3. **统一依赖管理**: 所有Python依赖安装在项目venv中
4. **定期更新**: 使用 `git pull origin main` 更新源码

## 📊 维护记录

| 项目 | 最后更新 | 版本 | 备注 |
|------|----------|------|------|
| MarkItDown | 2025-07-22 | latest | 正常运行 |
| kepano-obsidian | 2025-07-24 | latest | 正常运行 |
| bazi | 2025-08-04 | latest | 新增项目，功能正常 |

---

*按照外部服务管理规范维护，确保项目结构清晰、依赖关系明确*
